import React, { forwardRef, Fragment, useEffect, useMemo, useState } from 'react';

import type { Editor } from '@tiptap/core';

import ActionMenuButton from '@/Components/TipTapEditor/Partials/ActionMenuButton';
import type { ButtonViewReturnComponentProps } from '@/Components/TipTapEditor/types';
import { getShortcutKey } from '@/Components/TipTapEditor/utils/platform';
import { ANY_TODO } from '@/types/general';
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuTrigger,
} from '@/UI-Kit/Shadcn/dropdown-menu';

export interface Item {
    title: string;
    icon?: ANY_TODO;
    level?: number;
    isActive: NonNullable<ButtonViewReturnComponentProps['isActive']>;
    action?: ButtonViewReturnComponentProps['action'];
    style?: React.CSSProperties;
    shortcutKeys?: string[];
    disabled?: boolean;
    divider?: boolean;
    default?: boolean;
}

interface Props {
    editor: Editor;
    disabled?: boolean;
    // color?: string;
    // shortcutKeys?: string[];
    // maxHeight?: string | number;
    tooltip?: string;
    items?: Item[];
}

const HeadingButton = forwardRef<HTMLButtonElement, Props>(({ disabled, tooltip, items, editor }, ref) => {
    // Track the current heading state specifically
    const [currentHeadingState, setCurrentHeadingState] = useState<{
        isHeading: boolean;
        level?: number;
        isParagraph: boolean;
    }>({
        isHeading: false,
        level: undefined,
        isParagraph: false,
    });

    useEffect(() => {
        const updateHeadingState = () => {
            const isParagraph = editor.isActive('paragraph');
            let isHeading = false;
            let level: number | undefined;

            // Check for each heading level
            for (let i = 1; i <= 6; i++) {
                if (editor.isActive('heading', { level: i })) {
                    isHeading = true;
                    level = i;
                    break;
                }
            }

            setCurrentHeadingState({ isHeading, level, isParagraph });
        };

        // Update immediately
        updateHeadingState();

        // Listen for editor changes
        editor.on('selectionUpdate', updateHeadingState);
        editor.on('transaction', updateHeadingState);

        return () => {
            editor.off('selectionUpdate', updateHeadingState);
            editor.off('transaction', updateHeadingState);
        };
    }, [editor]);

    const active = useMemo(() => {
        const find: ANY_TODO = items?.find((k: ANY_TODO) => k.isActive());

        if (find && !find.default) {
            return {
                ...find,
            };
        }

        const item: Item = {
            title: tooltip as string,
            level: 0,
            isActive: () => false,
        };

        return item;
    }, [items, tooltip, currentHeadingState]);

    return (
        <DropdownMenu>
            <DropdownMenuTrigger disabled={disabled} asChild>
                <ActionMenuButton
                    ref={ref}
                    title={active?.title}
                    tooltip={tooltip}
                    disabled={disabled}
                    icon="ChevronDown"
                />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-full">
                {items?.map((item: ANY_TODO, index: number) => (
                    <Fragment key={`heading-k-${index.toString()}`}>
                        <DropdownMenuCheckboxItem checked={active?.title === item.title} onClick={item.action}>
                            <div className={`ml-1 h-full heading-${item.level}`}>{item.title}</div>
                            {!!item?.shortcutKeys?.length && (
                                <DropdownMenuShortcut className="pl-4">
                                    {item?.shortcutKeys
                                        ?.map((dropdownItem: ANY_TODO) => getShortcutKey(dropdownItem))
                                        .join(' ')}
                                </DropdownMenuShortcut>
                            )}
                        </DropdownMenuCheckboxItem>
                        {item.level === 0 && <DropdownMenuSeparator />}
                    </Fragment>
                ))}
            </DropdownMenuContent>
        </DropdownMenu>
    );
});

HeadingButton.displayName = 'HeadingButton';

export default HeadingButton;
