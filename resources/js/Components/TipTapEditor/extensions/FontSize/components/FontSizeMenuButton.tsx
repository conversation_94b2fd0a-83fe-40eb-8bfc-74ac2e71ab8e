import React, { useMemo } from 'react';

import type { Editor } from '@tiptap/core';

import { useActive } from '@/Components/TipTapEditor/hooks/useActive';
import { translate } from '@/Components/TipTapEditor/locales/translate';
import ActionMenuButton from '@/Components/TipTapEditor/Partials/ActionMenuButton';
import type { ButtonViewReturnComponentProps } from '@/Components/TipTapEditor/types';
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuTrigger,
} from '@/UI-Kit/Shadcn/dropdown-menu';

export interface Item {
    title: string;
    isActive: NonNullable<ButtonViewReturnComponentProps['isActive']>;
    action?: ButtonViewReturnComponentProps['action'];
    style?: React.CSSProperties;
    disabled?: boolean;
    divider?: boolean;
    default?: boolean;
}

interface IPropsFontSizeMenuButton {
    editor: Editor;
    disabled?: boolean;
    // color?: string;
    // shortcutKeys?: string[];
    // maxHeight?: string | number;
    tooltip?: string;
    items?: Item[];
}

function FontSizeMenuButton({ items, disabled, tooltip, editor }: IPropsFontSizeMenuButton) {
    // Use useActive hook to trigger re-renders when editor state changes
    const editorActive = useActive(editor, 'textStyle');

    const active = useMemo(() => {
        const find: any = (items || []).find((k: any) => k.isActive());
        if (find) {
            return find;
        }
        const item: Item = {
            title: translate('editor.fontSize.default.tooltip'),
            isActive: () => false,
        };
        return item;
    }, [items, editorActive]);

    return (
        <DropdownMenu>
            <DropdownMenuTrigger disabled={disabled} asChild>
                <ActionMenuButton title={active?.title} tooltip={`${tooltip}`} disabled={disabled} icon="ChevronDown" />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="max-h-96 w-32 overflow-y-auto">
                {items?.map((item: any, index) => (
                    <DropdownMenuCheckboxItem
                        key={`font-size-${index.toString()}`}
                        checked={active.title === item.title}
                        onClick={item.action}
                    >
                        <div className="ml-1 h-full">{item.title}</div>
                    </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
        </DropdownMenu>
    );
}

export default FontSizeMenuButton;
