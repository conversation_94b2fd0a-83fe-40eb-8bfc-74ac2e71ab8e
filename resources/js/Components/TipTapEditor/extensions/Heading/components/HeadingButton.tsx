import React, { forwardRef, Fragment, useMemo } from 'react';

import ActionMenuButton from '@/Components/TipTapEditor/Partials/ActionMenuButton';
import type { ButtonViewReturnComponentProps } from '@/Components/TipTapEditor/types';
import { getShortcutKey } from '@/Components/TipTapEditor/utils/platform';
import { ANY_TODO } from '@/types/general';
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuTrigger,
} from '@/UI-Kit/Shadcn/dropdown-menu';

export interface Item {
    title: string;
    icon?: ANY_TODO;
    level?: number;
    isActive: NonNullable<ButtonViewReturnComponentProps['isActive']>;
    action?: ButtonViewReturnComponentProps['action'];
    style?: React.CSSProperties;
    shortcutKeys?: string[];
    disabled?: boolean;
    divider?: boolean;
    default?: boolean;
}

interface Props {
    // editor:ANY_TODO;
    disabled?: boolean;
    // color?: string;
    // shortcutKeys?: string[];
    // maxHeight?: string | number;
    tooltip?: string;
    items?: Item[];
}

const HeadingButton = forwardRef<HTMLButtonElement, Props>(({ disabled, tooltip, items }, ref) => {
    const active = useMemo(() => {
        const find: ANY_TODO = items?.find((k: ANY_TODO) => k.isActive());

        if (find && !find.default) {
            return {
                ...find,
            };
        }

        const item: Item = {
            title: tooltip as string,
            level: 0,
            isActive: () => false,
        };

        return item;
    }, [items, tooltip]);

    return (
        <DropdownMenu>
            <DropdownMenuTrigger disabled={disabled} asChild>
                <ActionMenuButton
                    ref={ref}
                    title={active?.title}
                    tooltip={tooltip}
                    disabled={disabled}
                    icon="ChevronDown"
                />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-full">
                {items?.map((item: ANY_TODO, index: number) => (
                    <Fragment key={`heading-k-${index.toString()}`}>
                        <DropdownMenuCheckboxItem checked={active?.title === item.title} onClick={item.action}>
                            <div className={`ml-1 h-full heading-${item.level}`}>{item.title}</div>
                            {!!item?.shortcutKeys?.length && (
                                <DropdownMenuShortcut className="pl-4">
                                    {item?.shortcutKeys
                                        ?.map((dropdownItem: ANY_TODO) => getShortcutKey(dropdownItem))
                                        .join(' ')}
                                </DropdownMenuShortcut>
                            )}
                        </DropdownMenuCheckboxItem>
                        {item.level === 0 && <DropdownMenuSeparator />}
                    </Fragment>
                ))}
            </DropdownMenuContent>
        </DropdownMenu>
    );
});

HeadingButton.displayName = 'HeadingButton';

export default HeadingButton;
