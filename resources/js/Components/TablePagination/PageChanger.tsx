import { type JSX, useEffect, useState } from 'react';

import { icon } from '@fortawesome/fontawesome-svg-core/import.macro';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';

import { theme } from '@/Components/TablePagination/config';
import { makePageArray } from '@/Components/TablePagination/utils';
import { cn } from '@/lib/utils';
import { PageChangerProps } from '@/types/table-pagination';

const PageChanger = ({
    current,
    pageCount,
    onPageNumClick,
    isStacked,
    maxSegmentList,
}: PageChangerProps): JSX.Element | null => {
    const [pageArray, setPageArray] = useState<number[]>([]);

    useEffect(() => {
        if (current && pageCount) {
            const pages = makePageArray(pageCount, current, maxSegmentList);
            if (pages) {
                setPageArray([...pages]);
            }
        }
    }, [current, pageCount]);

    if (!current || !pageCount || !pageArray) return null;

    return (
        <nav
            className={cn('inline-flex space-x-1', { 'flex w-full': isStacked, 'md:-mt-px': !isStacked })}
            data-ondata="page-changer"
            aria-label="Pagination"
        >
            <button
                type="button"
                className={cn(theme.baseButton, theme.baseColors, {
                    [theme.disabledClass]: current === 1,
                })}
                disabled={current === 1}
                onClick={e => onPageNumClick(current - 1, e)}
            >
                <span className="sr-only">Previous</span>
                <span className={theme.numberClass}>
                    <FontAwesomeIcon icon={icon({ name: 'arrow-left', style: 'solid' })} />
                </span>
            </button>
            {pageArray.map((num, index) =>
                num !== -9 ? (
                    <button
                        type="button"
                        key={`array-${index.toString()}-${num}`}
                        aria-current="page"
                        className={cn(theme.baseButton, {
                            [theme.baseColors]: num !== current,
                            [theme.currentColors]: num === current,
                        })}
                        onClick={e => onPageNumClick(num, e)}
                    >
                        <span className={theme.numberClass}>{num}</span>
                    </button>
                ) : (
                    <span className={theme.ellipsisClass} key={`array-non-num${index.toString()}`}>
                        <FontAwesomeIcon icon={icon({ name: 'ellipsis', style: 'regular' })} />
                    </span>
                )
            )}

            <button
                type="button"
                className={cn(theme.baseButton, theme.baseColors, {
                    [theme.disabledClass]: current === pageCount,
                })}
                onClick={e => onPageNumClick(current + 1, e)}
            >
                <span className="sr-only">Next</span>
                <span className={theme.numberClass}>
                    <FontAwesomeIcon icon={icon({ name: 'arrow-right', style: 'solid' })} />
                </span>
            </button>
        </nav>
    );
};

export default PageChanger;
