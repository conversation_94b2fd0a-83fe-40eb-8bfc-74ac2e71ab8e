import React, { Fragment, useEffect, useMemo, useState } from 'react';

import { translate } from '@/Components/TipTapEditor/locales/translate';
import ActionMenuButton from '@/Components/TipTapEditor/Partials/ActionMenuButton';
import type { ButtonViewReturnComponentProps } from '@/Components/TipTapEditor/types';
import { ANY_TODO } from '@/types/general';
import {
    DropdownMenu,
    DropdownMenuCheckboxItem,
    DropdownMenuContent,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/UI-Kit/Shadcn/dropdown-menu';

export interface Item {
    title: string;
    icon?: ANY_TODO;
    font?: string;
    isActive: NonNullable<ButtonViewReturnComponentProps['isActive']>;
    action?: ButtonViewReturnComponentProps['action'];
    style?: React.CSSProperties;
    shortcutKeys?: string[];
    disabled?: boolean;
    divider?: boolean;
    default?: boolean;
}

interface Props {
    editor: ANY_TODO;
    disabled?: boolean;
    // color?: string;
    // shortcutKeys?: string[];
    // maxHeight?: string | number;
    tooltip?: string;
    items?: Item[];
}

const FontFamilyButton = ({ editor, disabled, tooltip, items }: Props) => {
    // Track the current fontFamily attribute specifically
    const [currentFontFamily, setCurrentFontFamily] = useState<string | undefined>(undefined);

    useEffect(() => {
        const updateFontFamily = () => {
            const { fontFamily } = editor.getAttributes('textStyle');
            setCurrentFontFamily(fontFamily);
        };

        // Update immediately
        updateFontFamily();

        // Listen for editor changes
        editor.on('selectionUpdate', updateFontFamily);
        editor.on('transaction', updateFontFamily);

        return () => {
            editor.off('selectionUpdate', updateFontFamily);
            editor.off('transaction', updateFontFamily);
        };
    }, [editor]);

    const active = useMemo(() => {
        const find: ANY_TODO = items?.find((k: ANY_TODO) => k.isActive());

        if (find && !find.default) {
            return {
                ...find,
            };
        }

        const item: Item = {
            title: tooltip as ANY_TODO,
            font: translate('editor.fontFamily.default.tooltip'),
            isActive: () => false,
            disabled: false,
            action: () => editor.chain().focus().unsetFontFamily().run(),
        };

        return item;
    }, [translate, tooltip, disabled, items, currentFontFamily]);

    return (
        <DropdownMenu>
            <DropdownMenuTrigger disabled={disabled} asChild>
                <ActionMenuButton
                    title={active?.font?.length > 7 ? `${active?.font?.slice(0, 6)}...` : active?.font}
                    tooltip={tooltip}
                    disabled={disabled}
                    icon="ChevronDown"
                />
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-full">
                {items?.map((item: ANY_TODO, index) => {
                    const style =
                        item.font === translate('editor.fontFamily.default.tooltip') ? {} : { fontFamily: item.font };

                    return (
                        <Fragment key={`font-family-${index.toString()}`}>
                            <DropdownMenuCheckboxItem checked={active?.font === item.font} onClick={item.action}>
                                <div className="ml-1 h-full" style={style}>
                                    {item.font}
                                </div>
                            </DropdownMenuCheckboxItem>
                            {item.font === translate('editor.fontFamily.default.tooltip') && <DropdownMenuSeparator />}
                        </Fragment>
                    );
                })}
            </DropdownMenuContent>
        </DropdownMenu>
    );
};

export default FontFamilyButton;
